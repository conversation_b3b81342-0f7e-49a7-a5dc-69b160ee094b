# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: package editor-ui languages

on:
  schedule:
  - cron: "0 * * * *"
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x]
    steps:
    - name: Get latest GitHub release version
      id: latest_release
      run: |
        LATEST_VERSION=$(curl -s https://api.github.com/repos/n8n-io/n8n/releases/latest | jq -r '.tag_name')
        echo "Latest release tag: $LATEST_VERSION"
        echo "latest=$LATEST_VERSION" >> $GITHUB_OUTPUT

    - name: Checkout local
      uses: actions/checkout@v4
      with:
        path: ./n8n-i18n-chinese

    # - name: Read version from package.json
    #   id: get_version
    #   run: |
    #     VERSION=$(jq -r '.version' package.json)
    #     echo "Version: $VERSION"
    #     echo "version=$VERSION" >> $GITHUB_OUTPUT
    #   working-directory: /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n

    - name: Check if tag exists
      id: check_tag
      run: |
        TAG="${{ steps.latest_release.outputs.latest }}"
        echo "Checking for remote tag: $TAG"
        if git ls-remote --tags origin | grep "refs/tags/$TAG$"; then
          echo "exists=true" >> $GITHUB_OUTPUT
        else
          echo "exists=false" >> $GITHUB_OUTPUT
        fi
      working-directory: /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n-i18n-chinese

    - name: Exit if tags exist
      if: steps.check_tag.outputs.exists == 'true'
      run: |
        echo "Tags exist, stopping workflow."

    - name: Checkout n8n
      if: steps.check_tag.outputs.exists == 'false'
      uses: actions/checkout@v4
      with:
        repository: n8n-io/n8n
        ref: ${{ steps.latest_release.outputs.latest }}
        path: ./n8n

    - uses: actions/setup-node@v4.2.0
      if: steps.check_tag.outputs.exists == 'false'
      with:
        node-version: 20.x

    - name: Setup corepack and pnpm
      if: steps.check_tag.outputs.exists == 'false'
      run: |
        npm i -g corepack@0.31
        corepack enable

    - run: pnpm install --frozen-lockfile
      if: steps.check_tag.outputs.exists == 'false'
      working-directory: /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n

    - name: check n8n editor-ui is old
      id: editorui_dir
      run: |
        DIR_PATH="/home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n/packages/frontend/editor-ui"       # 你要检查的目录
        FALLBACK_DIR="/home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n/packages/editor-ui" # 备用目录

        if [ -d "$DIR_PATH" ]; then
          echo "flag=new" >> $GITHUB_OUTPUT
          echo "dir=$DIR_PATH" >> $GITHUB_OUTPUT
        else
          echo "flag=old" >> $GITHUB_OUTPUT
          echo "dir=$FALLBACK_DIR" >> $GITHUB_OUTPUT
        fi

    - name: Move editor-ui i18n language
      if: steps.check_tag.outputs.exists == 'false'
      run:     cp -r /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n-i18n-chinese/languages/* "/home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n/packages/frontend/@n8n/i18n/src/locales/"

    - name: Patch New editor-ui
      if: steps.editorui_dir.outputs.flag == 'new' && steps.check_tag.outputs.exists == 'false'
      run: git apply /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n-i18n-chinese/fix_editor-ui.patch
      working-directory: /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n

    - name: Patch Old editor-ui
      if: steps.editorui_dir.outputs.flag == 'old' && steps.check_tag.outputs.exists == 'false'
      run: git apply /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n-i18n-chinese/fix_editor-ui.old.patch
      working-directory: /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n

    - name: Pnpm build editor-ui
      if: steps.check_tag.outputs.exists == 'false'
      run: pnpm run build:frontend --filter=n8n-editor-ui
      working-directory: /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n

    - name: 打包 dist 产物
      if: steps.check_tag.outputs.exists == 'false'
      run: |
        tar -czvf editor-ui.tar.gz dist
      working-directory: ${{ steps.editorui_dir.outputs.dir }}

    - name: 拷贝 dist 到项目目录
      if: steps.check_tag.outputs.exists == 'false'
      run: |
        cp -r ${{ steps.editorui_dir.outputs.dir }}/dist /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n-i18n-chinese/editor-ui-dist

    - name: Update docker-compose.yml version
      if: steps.check_tag.outputs.exists == 'false'
      run: |
        RAW_TAG="${{ steps.latest_release.outputs.latest }}"
        VERSION="${RAW_TAG#n8n@}"  # 去掉前缀 n8n@
        echo "Replacing {version} with $VERSION in docker-compose.yml"
        sed -i "s/{version}/$VERSION/g" /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n-i18n-chinese/docker-compose.yml

    - name: 创建 git tag
      if: steps.check_tag.outputs.exists == 'false'
      run: |
        TAG="${{ steps.latest_release.outputs.latest }}"
        git config --global user.name "github-actions"
        git config --global user.email "<EMAIL>"
        git add editor-ui-dist
        git add docker-compose.yml
        git commit -m "chore: add built editor-ui dist for ${{ steps.latest_release.outputs.latest }}"
        git tag $TAG
        git push origin $TAG
      working-directory: /home/<USER>/work/n8n-i18n-chinese/n8n-i18n-chinese/n8n-i18n-chinese

    - name: 创建 GitHub Release
      if: steps.check_tag.outputs.exists == 'false'
      id: create_release
      uses: softprops/action-gh-release@v2
      with:
        tag_name: ${{ steps.latest_release.outputs.latest }}
        name: Release editor-ui to ${{ steps.latest_release.outputs.latest }}
        body: |
          自动发布的中文语言包版本。
        draft: false
        prerelease: false
        files: ${{ steps.editorui_dir.outputs.dir }}/editor-ui.tar.gz
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

